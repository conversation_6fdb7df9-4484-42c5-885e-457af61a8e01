#!/usr/bin/env python3
# Test file for encryption function

from encr import encrpt

# Test the encryption function
def test_encryption():
    # Test with simple text and key
    plain_text = "HELLO"
    key = 3
    encrypted = encrpt(plain_text, key)
    print(f"Plain text: {plain_text}")
    print(f"Key: {key}")
    print(f"Encrypted: {encrypted}")
    
    # Test with text containing spaces and punctuation
    plain_text2 = "HELLO WORLD!"
    encrypted2 = encrpt(plain_text2, key)
    print(f"\nPlain text: {plain_text2}")
    print(f"Key: {key}")
    print(f"Encrypted: {encrypted2}")

if __name__ == "__main__":
    test_encryption()
